using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class StoryChoice
{
    public string text;
    public int affinity_change;
    public string next_scene;
    public string character_reaction;
}

[Serializable]
public class StoryScene
{
    public string character_name;
    public string character_image;
    public string location;
    public string background;
    public string dialogue;
    public string action_type;
    public string next_scene;
    public List<StoryChoice> choices = new List<StoryChoice>();
}

[Serializable]
public class CharacterStory
{
    public Dictionary<string, StoryScene> scenes = new Dictionary<string, StoryScene>();
}

[Serializable]
public class StoryData
{
    public Dictionary<string, CharacterStory> characters = new Dictionary<string, CharacterStory>();
}