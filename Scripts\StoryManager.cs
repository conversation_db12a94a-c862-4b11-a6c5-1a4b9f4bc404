using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.SceneManagement;

public class StoryManager : MonoBehaviour
{
    [Header("Story Settings")]
    public TextAsset storyJsonFile;
    
    private UIDocument storyDocument;
    private StoryData storyData;
    private string currentCharacter = "Maimy";
    private string currentScene = "scene_intro";
    private string userName = "???";
    private int affinity = 0;
    
    // UI Elements
    private Label characterNameLabel;
    private Label dialogueLabel;
    private VisualElement characterImage;
    private VisualElement backgroundImage;
    private VisualElement choicesContainer;
    private VisualElement nameInputContainer;
    private Button nextButton;
    
    private void OnEnable()
    {
        LoadStoryData();
        SetupUI();
        StartStory();
    }
    
    private void LoadStoryData()
    {
        if (storyJsonFile != null)
        {
            string jsonText = storyJsonFile.text;
            storyData = JsonUtility.FromJson<StoryData>(jsonText);
        }
    }
    
    private void SetupUI()
    {
        storyDocument = GetComponent<UIDocument>();
        var root = storyDocument.rootVisualElement;
        
        characterNameLabel = root.Q<Label>("character-name");
        dialogueLabel = root.Q<Label>("dialogue-text");
        characterImage = root.Q("character-image");
        backgroundImage = root.Q("background-image");
        choicesContainer = root.Q("choices-container");
        nameInputContainer = root.Q("name-input-container");
        nextButton = root.Q<Button>("next-button");
        
        if (nextButton != null)
        {
            nextButton.clicked += OnNextClicked;
        }
    }
    
    private void StartStory()
    {
        RenderScene(currentScene);
    }
    
    private void RenderScene(string sceneKey)
    {
        currentScene = sceneKey;
        
        if (!storyData.characters.ContainsKey(currentCharacter) ||
            !storyData.characters[currentCharacter].scenes.ContainsKey(sceneKey))
        {
            Debug.LogError($"Scene {sceneKey} not found for character {currentCharacter}");
            return;
        }
        
        var scene = storyData.characters[currentCharacter].scenes[sceneKey];
        
        // Update UI
        UpdateBackground(scene.background);
        UpdateCharacterImage(scene.character_image);
        UpdateCharacterName(scene.character_name);
        UpdateDialogue(scene.dialogue);
        
        // Handle special actions
        if (scene.action_type == "INPUT_NAME")
        {
            ShowNameInput(scene);
        }
        else if (scene.choices != null && scene.choices.Count > 0)
        {
            ShowChoices(scene.choices);
        }
        else if (!string.IsNullOrEmpty(scene.next_scene))
        {
            // Auto advance after delay
            StartCoroutine(AutoAdvanceCoroutine(scene.next_scene));
        }
    }
    
    private void UpdateBackground(string backgroundPath)
    {
        if (backgroundImage != null && !string.IsNullOrEmpty(backgroundPath))
        {
            var texture = Resources.Load<Texture2D>(backgroundPath);
            if (texture != null)
            {
                backgroundImage.style.backgroundImage = new StyleBackground(texture);
            }
        }
    }
    
    private void UpdateCharacterImage(string imagePath)
    {
        if (characterImage != null && !string.IsNullOrEmpty(imagePath))
        {
            var texture = Resources.Load<Texture2D>(imagePath);
            if (texture != null)
            {
                characterImage.style.backgroundImage = new StyleBackground(texture);
            }
        }
    }
    
    private void UpdateCharacterName(string name)
    {
        if (characterNameLabel != null)
        {
            characterNameLabel.text = name;
        }
    }
    
    private void UpdateDialogue(string dialogue)
    {
        if (dialogueLabel != null)
        {
            string processedDialogue = dialogue.Replace("<USER_NAME>", userName);
            StartCoroutine(TypeText(processedDialogue));
        }
    }
    
    private IEnumerator TypeText(string text)
    {
        dialogueLabel.text = "";
        foreach (char c in text)
        {
            dialogueLabel.text += c;
            yield return new WaitForSeconds(0.03f);
        }
    }
    
    private void ShowChoices(List<StoryChoice> choices)
    {
        if (choicesContainer == null) return;
        
        choicesContainer.Clear();
        choicesContainer.style.display = DisplayStyle.Flex;
        
        for (int i = 0; i < choices.Count; i++)
        {
            var choice = choices[i];
            var choiceButton = new Button();
            choiceButton.text = choice.text;
            choiceButton.AddToClassList("choice-button");
            
            int choiceIndex = i;
            choiceButton.clicked += () => OnChoiceSelected(choiceIndex);
            
            choicesContainer.Add(choiceButton);
        }
    }
    
    private void ShowNameInput(StoryScene scene)
    {
        if (nameInputContainer == null) return;
        
        nameInputContainer.Clear();
        nameInputContainer.style.display = DisplayStyle.Flex;
        
        var nameField = new TextField("Masukkan nama anda:");
        nameField.AddToClassList("name-input");
        
        var submitButton = new Button();
        submitButton.text = "Confirm";
        submitButton.AddToClassList("submit-button");
        
        submitButton.clicked += () => {
            userName = string.IsNullOrEmpty(nameField.value) ? "Sayang" : nameField.value;
            nameInputContainer.style.display = DisplayStyle.None;
            RenderScene(scene.next_scene);
        };
        
        nameInputContainer.Add(nameField);
        nameInputContainer.Add(submitButton);
    }
    
    private void OnChoiceSelected(int choiceIndex)
    {
        var scene = storyData.characters[currentCharacter].scenes[currentScene];
        if (choiceIndex < scene.choices.Count)
        {
            var choice = scene.choices[choiceIndex];
            affinity += choice.affinity_change;
            
            choicesContainer.style.display = DisplayStyle.None;
            RenderScene(choice.next_scene);
        }
    }
    
    private void OnNextClicked()
    {
        var scene = storyData.characters[currentCharacter].scenes[currentScene];
        if (!string.IsNullOrEmpty(scene.next_scene))
        {
            RenderScene(scene.next_scene);
        }
    }
    
    private IEnumerator AutoAdvanceCoroutine(string nextScene)
    {
        yield return new WaitForSeconds(2f);
        RenderScene(nextScene);
    }
}