<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
  <title>Story Mode</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="css/story.css" />
</head>
<body>
  <div class="vn-root">
    <div class="vn-stage" id="stage">
      <div class="vn-bg" id="bg"></div>
      <div class="vn-dim"></div>

      <div class="vn-topbar">
        <button class="chip" id="autoBtn">AUTO</button>
        <button class="chip" id="logBtn">📜 LOG</button>
      </div>

      <div class="vn-character" id="charWrap">
        <img id="charImg" alt="character" src="" />
      </div>

      <div class="vn-choices hidden" id="choices"></div>

      <div class="vn-dialog" id="dialog">
        <div class="panel">
          <div class="vn-name"><span class="bar"></span><span id="name">Name</span></div>
          <div class="vn-text" id="text"></div>
          <div class="vn-next" id="next">❯</div>
        </div>
      </div>

      <div class="vn-log" id="logModal">
        <button class="close" id="logClose">Close</button>
        <div class="sheet">
          <h4>Dialogue Log</h4>
          <div id="logLines"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
  // Embed story data directly to avoid CORS issues
  const STORY_DATA = <?php echo file_get_contents('data/story.json'); ?>;
  
  (function(){
    const qs = new URLSearchParams(location.search);
    const charKey = qs.get('char') || 'Maimy';
    let sceneKey = qs.get('scene') || 'scene_intro';

    const el = {
      name: document.getElementById('name'),
      text: document.getElementById('text'),
      next: document.getElementById('next'),
      choices: document.getElementById('choices'),
      charImg: document.getElementById('charImg'),
      bg: document.getElementById('bg'),
      autoBtn: document.getElementById('autoBtn'),
      logBtn: document.getElementById('logBtn'),
      dialog: document.getElementById('dialog'),
      logModal: document.getElementById('logModal'),
      logClose: document.getElementById('logClose'),
      logLines: document.getElementById('logLines')
    };

    const state = {
      story: STORY_DATA,
      affinity:0,
      typing:false,
      auto:false,
      log:[],
      userName: '???' // Default unknown user name
    };

    // helpers
    const sleep = (ms)=> new Promise(r=>setTimeout(r,ms));
    async function typeText(target, text){
      state.typing = true; target.textContent='';
      const speed = 16 + Math.random()*8; // ~16ms per char
      for(let i=0;i<text.length;i++){
        target.textContent += text[i];
        await sleep(speed);
        if(!state.typing){ target.textContent = text; break; }
      }
      state.typing = false;
    }

    // Click effect functions
    function createClickEffect(e) {
      const effect = document.createElement('div');
      effect.className = 'click-effect';

      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      effect.style.left = (x - 10) + 'px';
      effect.style.top = (y - 10) + 'px';

      e.currentTarget.appendChild(effect);

      setTimeout(() => {
        if (effect.parentNode) {
          effect.parentNode.removeChild(effect);
        }
      }, 600);
    }

    function createChoiceClickEffect(e) {
      const effect = document.createElement('div');
      effect.className = 'choice-click-effect';

      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      effect.style.left = (x - 15) + 'px';
      effect.style.top = (y - 15) + 'px';

      e.currentTarget.appendChild(effect);

      setTimeout(() => {
        if (effect.parentNode) {
          effect.parentNode.removeChild(effect);
        }
      }, 500);
    }

    function setAuto(on){ state.auto = on; el.autoBtn.classList.toggle('is-active', on); }

    function pushLog(name, text){
      state.log.push({name, text});
      const div = document.createElement('div');
      div.className = 'line';

      // Only add selected choices to log (skip choice options)
      if(name.includes('Pilihan')){
        // Don't add choice options to log, just return
        return;
      } else if(name.includes('Dipilih')){
        // Use user name instead of "Dipilih"
        div.classList.add('choice-selected');
        div.style.background = 'rgba(34,197,94,.1)';
        div.style.borderLeft = '3px solid #22c55e';
        div.style.fontWeight = '600';
        div.textContent = state.userName + ': ' + text;
      } else {
        div.textContent = name + ': ' + text;
      }

      el.logLines.appendChild(div);
    }

    // Function to update user name when discovered in story
    function setUserName(name) {
      state.userName = name || '???';
    }

    function setCharacter(img){
      el.charImg.src = img || "assets/characters/maimy/maimysenyummanis.png";
    }

    // Function to update background based on scene
    function updateBackground(backgroundPath, location) {
      const bgElement = document.querySelector('.vn-bg');

      // Default fallback to paradise garden
      let finalBackground = 'assets/background/taman paradise (1).png';

      // If background path is provided, try to use it
      if(backgroundPath) {
        finalBackground = backgroundPath;
      } else {
        // Map locations to backgrounds
        const locationMap = {
          'Hutan Mistik': 'assets/background/kota.png',
          'Hutan Kabus': 'assets/background/hutan_kabur.png',
          'Lembah Lumut': 'assets/background/lembah_lumut.png',
          'Istana Impian': 'assets/background/istana_impian.png',
          'Taman Paradise': 'assets/background/taman paradise (1).png',
          'Istana': 'assets/background/taman paradise (2).png',
          'Pantai': 'assets/background/taman paradise (3).png',
          'Kota': 'assets/background/kota.png'
        };

        if(location && locationMap[location]) {
          finalBackground = locationMap[location];
        }
      }

      console.log('Setting background:', finalBackground);

      // URL encode the path to handle spaces
      const encodedPath = encodeURI(finalBackground);
      console.log('Encoded path:', encodedPath);

      // Apply background to the vn-bg element
      bgElement.style.background = `linear-gradient(135deg, rgba(30,41,59,0.3) 0%, rgba(51,65,85,0.3) 50%, rgba(30,41,59,0.3) 100%), url("${encodedPath}") center/cover no-repeat`;
      bgElement.style.filter = 'saturate(.9) brightness(.8)';

      // Also try with fallback if main fails
      const img = new Image();
      img.onload = () => {
        console.log('Background loaded successfully:', finalBackground);
      };
      img.onerror = () => {
        console.log('Background failed, using fallback');
        const fallbackPath = encodeURI('assets/background/taman paradise (1).png');
        bgElement.style.background = `linear-gradient(135deg, rgba(30,41,59,0.3) 0%, rgba(51,65,85,0.3) 50%, rgba(30,41,59,0.3) 100%), url("${fallbackPath}") center/cover no-repeat`;
      };
      img.src = finalBackground;
    }

    async function renderScene(key){
      sceneKey = key;
      console.log('Rendering scene:', key, 'for character:', charKey);

      // Clear any existing choices/inputs first
      el.choices.classList.add('hidden');
      el.choices.classList.remove('show');
      el.choices.innerHTML = '';

      const scene = state.story?.[charKey]?.[key];
      console.log('Scene data:', scene);
      if(!scene){
        await typeText(el.text, `Scene "${key}" not found for character "${charKey}".`);
        return;
      }

      // Update background image
      console.log('Scene background:', scene.background, 'Location:', scene.location);
      updateBackground(scene.background, scene.location);

      setCharacter(scene.character_image);
      el.name.textContent = scene.character_name || 'Character';

      // Replace <USER_NAME> placeholder with actual user name
      let dialogue = scene.dialogue || '';
      dialogue = dialogue.replace(/<USER_NAME>/g, state.userName);

      pushLog(el.name.textContent, dialogue);
      await typeText(el.text, dialogue);
      el.next.style.opacity = 1;

      // Handle special action types
      if (scene.action_type === 'INPUT_NAME') {
        showNameInput(scene);
      } else if (scene.choices && scene.choices.length > 0) {
        // show choices if they exist
        showChoices(scene.choices);
      } else if (scene.next_scene) {
        // auto-advance to next scene if no choices
        setTimeout(() => {
          renderScene(scene.next_scene);
        }, 2000); // Wait 2 seconds before auto-advancing
      }

      // autoplay
      if(state.auto){
        if(scene.choices && scene.choices.length){ await sleep(1200); pickChoice(0); }
      }
    }

    function showNameInput(scene) {
      const wrap = el.choices;
      wrap.innerHTML = '';
      wrap.classList.remove('hidden');
      wrap.classList.add('show');

      // Create name input form
      const inputContainer = document.createElement('div');
      inputContainer.className = 'name-input-container';
      inputContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        padding: 20px;
        background: rgba(0,0,0,0.8);
        border-radius: 15px;
        border: 2px solid #ff6b9d;
      `;

      const nameInput = document.createElement('input');
      nameInput.type = 'text';
      nameInput.placeholder = 'Masukkan nama awak...';
      nameInput.className = 'name-input';
      nameInput.style.cssText = `
        padding: 12px 20px;
        font-size: 16px;
        border: 2px solid #ff6b9d;
        border-radius: 25px;
        background: rgba(255,255,255,0.1);
        color: white;
        text-align: center;
        outline: none;
        min-width: 250px;
      `;

      const submitBtn = document.createElement('button');
      submitBtn.textContent = 'Teruskan';
      submitBtn.className = 'vn-choice';
      submitBtn.style.cssText = `
        padding: 12px 30px;
        font-size: 16px;
        background: linear-gradient(45deg, #ff6b9d, #c44569);
        border: none;
        border-radius: 25px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
      `;

      submitBtn.onclick = (e) => {
        createChoiceClickEffect(e);
        const userName = nameInput.value.trim() || 'Sayang';
        setUserName(userName);

        // Update all future dialogue with user name
        state.userName = userName;

        // Log the name input
        pushLog('Nama dimasukkan', userName);

        // Hide the name input container immediately
        wrap.classList.add('hidden');
        wrap.classList.remove('show');
        wrap.innerHTML = '';

        // Proceed to next scene
        renderScene(scene.next_scene);
      };

      // Allow Enter key to submit
      nameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          submitBtn.click();
        }
      });

      inputContainer.appendChild(nameInput);
      inputContainer.appendChild(submitBtn);
      wrap.appendChild(inputContainer);

      // Focus on input
      setTimeout(() => nameInput.focus(), 100);
    }

    function showChoices(choices){
      const wrap = el.choices;
      wrap.innerHTML = '';

      if(!choices.length){
        wrap.classList.add('hidden');
        return;
      }

      // Log choices for dialogue history
      choices.forEach((c,idx)=>{
        pushLog('Pilihan ' + (idx + 1), c.text);
      });

      // Show choices with animation
      wrap.classList.remove('hidden');
      wrap.classList.add('show');

      choices.forEach((c,idx)=>{
        const b = document.createElement('button');
        b.className = 'vn-choice';
        b.textContent = c.text;
        b.onclick = (e)=> {
          createChoiceClickEffect(e);
          // Log the selected choice
          pushLog('Dipilih', c.text);
          pickChoice(idx);
        };
        wrap.appendChild(b);
      });
    }

    function pickChoice(index){
      const scene = state.story?.[charKey]?.[sceneKey];
      if(!scene || !scene.choices) return;
      const choice = scene.choices[index];
      if(!choice) return;
      state.affinity += Number(choice.affinity_change||0);

      // Hide choices
      el.choices.classList.add('hidden');
      el.choices.classList.remove('show');

      renderScene(choice.next_scene);
    }

    // interactions
    el.dialog.addEventListener('click', (e)=>{
      createClickEffect(e);
      if(state.typing){ state.typing=false; return; }
      // if no choices, proceed automatically (no-op here since scenes always have choices in sample)
    });

    // Add click effect to character image
    el.charImg.addEventListener('click', (e)=>{
      createClickEffect(e);
    });

    // Initialize and start story
    console.log('Story loaded:', state.story);
    renderScene(sceneKey);

    el.autoBtn.onclick = ()=> setAuto(!state.auto);
    el.logBtn.onclick = ()=> {
      el.logModal.style.display='flex';
      el.logModal.classList.add('show');
    };
    el.logClose.onclick = ()=> {
      el.logModal.classList.remove('show');
      setTimeout(()=> el.logModal.style.display='none', 300);
    };


  })();
  </script>
</body>
</html>
